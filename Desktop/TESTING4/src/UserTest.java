import java.util.ArrayList;
import java.util.Scanner;

public class UserTest {
    static ArrayList<User> users = new ArrayList<>();
    static Scanner scanner = new Scanner(System.in);
    
    public static void register() {
        System.out.print("Enter name: ");
        String name = scanner.nextLine();
        
        System.out.print("Enter email: ");
        String email = scanner.nextLine();
        
        System.out.print("Enter password: ");
        String password = scanner.nextLine();
        
        System.out.print("Confirm password: ");
        String confirmPassword = scanner.nextLine();
        
        if (password.equals(confirmPassword)) {
            users.add(new User(name, email, password));
            System.out.println("Registration successful!");
        } else {
            System.out.println("Passwords don't match!");
        }
    }
    
    public static void login() {
        System.out.print("Enter email: ");
        String email = scanner.nextLine();
        
        System.out.print("Enter password: ");
        String password = scanner.nextLine();
        
        for (User user : users) {
            if (user.email.equals(email) && user.password.equals(password)) {
                System.out.println("Login successful! Welcome " + user.name);
                return;
            }
        }
        System.out.println("Login failed!");
    }
    
    public static void main(String[] args) {
        while (true) {
            System.out.println("\n1. Register");
            System.out.println("2. Login");
            System.out.println("3. Exit");
            System.out.print("Choose: ");
            
            int choice = scanner.nextInt();
            scanner.nextLine(); // consume newline
            
            if (choice == 1) {
                register();
            } else if (choice == 2) {
                login();
            } else if (choice == 3) {
                break;
            }
        }
    }
}